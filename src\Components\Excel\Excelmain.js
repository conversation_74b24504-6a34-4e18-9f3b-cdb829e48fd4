import {
  createUniver,
  defaultTheme,
  LocaleType,
  merge,
} from "@univerjs/presets";
import { UniverSheetsCorePreset } from "@univerjs/presets/preset-sheets-core";
import UniverPresetSheetsCoreEnUS from "@univerjs/presets/preset-sheets-core/locales/en-US";

import "./style.css";
import "@univerjs/presets/lib/styles/preset-sheets-core.css";

import { BooleanNumber, SheetTypes } from "@univerjs/core";
import { LocaleType as CoreLocaleType } from "@univerjs/core";
import {
  useCallback,
  useEffect,
  useRef,
  useState,
  useImperativeHandle,
  forwardRef,
} from "react";
import { ToastContainer, Zoom } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

// Simple debounce utility to delay execution of a function
function debounce(func, delay) {
  let timeoutId;
  return function (...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(this, args), delay);
  };
}

// Convert A-Z column letters to number index (0-based)
export const letterToColumn = (letter) => {
  let col = 0;
  for (let i = 0; i < letter.length; i++) {
    col = col * 26 + (letter.charCodeAt(i) - 65 + 1);
  }
  return col - 1;
};

// Convert number index to A-Z column letter
export const columnToLetter = (col) => {
  let letter = "";
  while (col >= 0) {
    letter = String.fromCharCode((col % 26) + 65) + letter;
    col = Math.floor(col / 26) - 1;
  }
  return letter;
};

// Convert "A1" -> { rowIndex, colIndex }
export const cellRefToIndices = (cellRef) => {
  const match = cellRef.match(/([A-Z]+)(\d+)/);
  if (!match) return null;
  const [, colLetter, rowNumber] = match;
  const rowIndex = parseInt(rowNumber, 10) - 1;
  const colIndex = letterToColumn(colLetter);
  return { rowIndex, colIndex };
};

export const transformApiToMatrix = (apiData) => {
  const cellData = {};
  let maxRow = 0;
  let maxCol = 0;

  if (apiData?.excelData) {
    Object?.entries(apiData.excelData).forEach(([cell, value]) => {
      const indices = cellRefToIndices(cell);
      if (!indices) return;
      const { rowIndex, colIndex } = indices;

      if (!cellData[rowIndex]) cellData[rowIndex] = {};

      if (typeof value === "string" && value.startsWith("=")) {
        cellData[rowIndex][colIndex] = {
          f: value,
          s: {
            ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
          },
        };
      } else {
        cellData[rowIndex][colIndex] = {
          v: value,
          s: {
            ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
          },
        };
      }

      maxRow = Math.max(maxRow, rowIndex);
      maxCol = Math.max(maxCol, colIndex);
    });
  }

  if (Array?.isArray(apiData?.maskedCells)) {
    apiData.maskedCells.forEach((cellRef) => {
      const indices = cellRefToIndices(cellRef);
      if (!indices) return;
      const { rowIndex, colIndex } = indices;

      if (!cellData[rowIndex]) cellData[rowIndex] = {};
      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};

      cellData[rowIndex][colIndex].s = {
        ...(cellData[rowIndex][colIndex].s || {}),
        bg: { rgb: "#FFFF00" },
        ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
      };

      if (apiData?.highlightAllMaskedCells) {
        cellData[rowIndex][colIndex].v = "";
      }
    });
  }

  return {
    id: `workbook-${Date.now()}`,
    name: "universheet",
    sheetOrder: ["sheet-01"],
    sheets: {
      "sheet-01": {
        type: SheetTypes.GRID,
        id: "sheet-01",
        name: "Sheet 1",
        cellData,
        rowCount: Math.max(maxRow + 1, 20),
        columnCount: Math.max(maxCol + 1, 20),
        defaultRowHeight: 28,
        defaultColumnWidth: 93,
      },
    },
  };
};

export const transformMatrixToApi = (cellData) => {
  const apiExcelData = {};
  if (!cellData) return { excelData: apiExcelData };

  Object.entries(cellData).forEach(([rowIndex, row]) => {
    Object.entries(row).forEach(([colIndex, cell]) => {
      const colLetter = columnToLetter(parseInt(colIndex));
      const rowNumber = parseInt(rowIndex) + 1;

      // Preserve the formatted value if available, otherwise use the raw value
      let valueToStore = cell.v;

      // If the cell has formatting and a display value, preserve it
      if (cell.m !== undefined && cell.m !== null && cell.m !== "") {
        // Use the formatted display value (cell.m) if available
        valueToStore = cell.m;
      } else if (cell.v !== undefined && cell.v !== null && cell.v !== "") {
        // Otherwise use the raw value
        valueToStore = cell.v;
        console.log(`Using raw value for ${colLetter}${rowNumber}:`, cell.v);
      }

      if (
        valueToStore !== undefined &&
        valueToStore !== null &&
        valueToStore !== ""
      ) {
        apiExcelData[`${colLetter}${rowNumber}`] = String(valueToStore);
      }
    });
  });
  return { excelData: apiExcelData };
};

// Fixed transformApiMatrixToSheetData with proper text alignment
export const transformApiMatrixToSheetData = (
  cellMatrix,
  maskedCells = [],
  highlightAllMaskedCells = false
) => {
  // Clone the cellMatrix to avoid mutating the original
  const cellData = JSON.parse(JSON.stringify(cellMatrix || {}));

  // First, ensure all cells have proper left alignment
  Object.entries(cellData).forEach(([rowIndex, row]) => {
    Object.entries(row).forEach(([colIndex, cell]) => {
      if (!cell.s) cell.s = {};
      // Remove any existing alignment properties that might cause right alignment
      delete cell.s.ht;
      delete cell.s.vt;
      delete cell.s.tb;
      // Set proper left alignment
      cell.s.ht = 1; // 1 = left, 2 = center, 3 = right
    });
  });

  // Apply masking: always remove values and formulas, only show highlighted color, maintain left alignment
  if (Array.isArray(maskedCells)) {
    maskedCells.forEach((cellRef) => {
      const indices = cellRefToIndices(cellRef);
      if (!indices) return;
      const { rowIndex, colIndex } = indices;
      if (!cellData[rowIndex]) cellData[rowIndex] = {};
      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};

      const style = {
        bg: { rgb: "#FFFF00" },
        ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
      };

      cellData[rowIndex][colIndex].s = style;
      cellData[rowIndex][colIndex].v = "";
      if (cellData[rowIndex][colIndex].f) delete cellData[rowIndex][colIndex].f;
    });
  }

  return {
    id: `workbook-${Date.now()}`,
    name: "universheet",
    sheetOrder: ["sheet-01"],
    sheets: {
      "sheet-01": {
        type: SheetTypes.GRID,
        id: "sheet-01",
        name: "Sheet 1",
        cellData,
        rowCount: 20,
        columnCount: 20,
        defaultRowHeight: 28,
        defaultColumnWidth: 93,
      },
    },
  };
};

const ExcelSheets = forwardRef(
  (
    {
      cellsData,
      maskedCells,
      SetExcelApiData,
      SetCellsFilled,
      cellsFilled,
      apiData,
      excelID,
      responseSubmitted,
      highlightAllMaskedCells = false,
    },
    ref
  ) => {
    const [workbookData, setWorkbookData] = useState(null);
    const univerCreatedRef = useRef(false);
    const univerAPIRef = useRef(null);
    const workbookDataRef = useRef(null);
    const pendingChangesRef = useRef(new Map()); // Track pending changes
    const debouncedHandleRef = useRef(null);

    // Expose method to force sync all data immediately
    useImperativeHandle(ref, () => ({
      syncData: () => {
        // Cancel any pending debounced calls
        if (debouncedHandleRef.current) {
          debouncedHandleRef.current.cancel?.();
        }
        // Process any pending changes immediately
        handleDataChangeImmediate();
      },
    }));

    // Helper function to check if all masked cells are filled in responseSubmitted
    const areAllMaskedCellsFilled = useCallback(() => {
      if (!responseSubmitted || !maskedCells || maskedCells.length === 0) {
        return false;
      }

      return maskedCells.every((cellRef) => {
        const value = responseSubmitted[cellRef];
        return value !== undefined && value !== null && value !== "";
      });
    }, [responseSubmitted, maskedCells]);

    useEffect(() => {
      console.log(cellsFilled, "apidata prop");
      let matrix;

      if (apiData?.cellMatrix && !responseSubmitted) {
        matrix = transformApiMatrixToSheetData(
          apiData.cellMatrix,
          maskedCells,
          highlightAllMaskedCells
        );
      } else if (responseSubmitted) {
        // Check if all masked cells are actually filled in responseSubmitted
        const allMaskedCellsFilled = areAllMaskedCellsFilled();

        if (allMaskedCellsFilled) {
          matrix = transformApiToMatrix({
            excelData: responseSubmitted,
            maskedCells,
            highlightAllMaskedCells,
          });
        } else {
          matrix = transformApiMatrixToSheetData(
            apiData.cellMatrix,
            maskedCells,
            highlightAllMaskedCells
          );
        }
      } else {
        matrix = transformApiToMatrix({
          excelData: apiData?.excelData,
          maskedCells,
          highlightAllMaskedCells,
        });
      }
      // Only proceed if we have a valid matrix
      if (matrix) {
        const initialWorkbook = {
          ...matrix,
          locale: CoreLocaleType.EN_US,
          appVersion: "3.0.0-alpha",
        };

        setWorkbookData(initialWorkbook);
        workbookDataRef.current = initialWorkbook;
        univerCreatedRef.current = false;
        pendingChangesRef.current.clear();
      }
    }, [
      apiData,
      maskedCells,
      highlightAllMaskedCells,
      responseSubmitted,
      areAllMaskedCellsFilled,
    ]);

    // Immediate data change handler (no debounce)
    const handleDataChangeImmediate = useCallback(() => {
      const univerAPI = univerAPIRef.current;
      const book = univerAPI?.getActiveWorkbook();
      const sheet = book?.getActiveSheet();
      if (!sheet) return;

      const refWorkbook = workbookDataRef.current;
      const existingCellData =
        refWorkbook?.sheets?.["sheet-01"]?.cellData || {};
      const updatedCellData = {};

      const sheetData = sheet.getSheet().getSnapshot();
      const maxRow = sheetData.rowCount - 1;
      const maxCol = sheetData.columnCount - 1;

      // Process all cells including pending changes
      for (let rowNum = 0; rowNum <= maxRow; rowNum++) {
        for (let colNum = 0; colNum <= maxCol; colNum++) {
          const cellKey = `${rowNum}-${colNum}`;
          const range = sheet.getRange(rowNum, colNum);
          const formula = range.getFormula() || "";
          let calculatedValue = range.getValue() || "";

          // Check if there's a pending change for this cell
          if (pendingChangesRef.current.has(cellKey)) {
            calculatedValue = pendingChangesRef.current.get(cellKey);
          }

          const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;
          const existingCell = existingCellData?.[rowNum]?.[colNum];
          const isMaskedCell = maskedCells.includes(cellRef);
          const hasData = formula || calculatedValue !== "" || isMaskedCell;

          if (hasData) {
            if (!updatedCellData[rowNum]) updatedCellData[rowNum] = {};
            const newCellData = {
              v: calculatedValue,
              t: 1,
              s: {
                ...(existingCell?.s || {}),
                ht: 1, // Ensure left alignment (1 = left, 2 = center, 3 = right)
              },
            };
            if (formula) {
              newCellData.f = formula;
            }
            if (isMaskedCell) {
              newCellData.s = {
                ...newCellData.s,
                bg: { rgb: "#FFFF00" },
                ht: 1, // Ensure left alignment for masked cells (1 = left, 2 = center, 3 = right)
              };
              if (highlightAllMaskedCells) {
                newCellData.v = "";
              }
            }
            updatedCellData[rowNum][colNum] = newCellData;
          }
        }
      }

      // Clear pending changes as they've been processed
      pendingChangesRef.current.clear();

      workbookDataRef.current = {
        ...refWorkbook,
        sheets: {
          ...refWorkbook.sheets,
          ["sheet-01"]: {
            ...refWorkbook.sheets["sheet-01"],
            cellData: updatedCellData,
          },
        },
      };

      SetExcelApiData(transformMatrixToApi(updatedCellData));

      if (highlightAllMaskedCells) {
        SetCellsFilled(false);
      } else {
        let anyFilled = false;
        for (const cellRef of maskedCells) {
          const { rowIndex, colIndex } = cellRefToIndices(cellRef);
          const cell = updatedCellData?.[rowIndex]?.[colIndex];
          if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
            anyFilled = true;
            break;
          }
        }
        SetCellsFilled(anyFilled);
      }
    }, [SetExcelApiData, SetCellsFilled, highlightAllMaskedCells, maskedCells]);

    // Enhanced data change handler that preserves formatting during paste operations
    const handleFormattedDataChange = useCallback(() => {
      try {
        const univerAPI = univerAPIRef.current;
        const book = univerAPI?.getActiveWorkbook();
        const sheet = book?.getActiveSheet();
        if (!sheet) return;

        const refWorkbook = workbookDataRef.current;
        const existingCellData =
          refWorkbook?.sheets?.["sheet-01"]?.cellData || {};
        const updatedCellData = {};

        const sheetData = sheet.getSheet().getSnapshot();
        const maxRow = sheetData.rowCount - 1;
        const maxCol = sheetData.columnCount - 1;

        // Process all cells and preserve formatting
        for (let rowNum = 0; rowNum <= maxRow; rowNum++) {
          for (let colNum = 0; colNum <= maxCol; colNum++) {
            const cellKey = `${rowNum}-${colNum}`;
            const range = sheet.getRange(rowNum, colNum);
            const formula = range.getFormula() || "";
            let calculatedValue = range.getValue() || "";

            // Check if there's a pending change for this cell
            if (pendingChangesRef.current.has(cellKey)) {
              calculatedValue = pendingChangesRef.current.get(cellKey);
            }

            const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;
            const existingCell = existingCellData?.[rowNum]?.[colNum];
            const isMaskedCell = maskedCells.includes(cellRef);
            const hasData = formula || calculatedValue !== "" || isMaskedCell;

            if (hasData) {
              if (!updatedCellData[rowNum]) updatedCellData[rowNum] = {};

              // Get the current cell's style and formatted display value from UniverJS
              const cellStyle = range.getStyle() || {};
              const displayValue = range.getDisplayValue() || calculatedValue;

              // Debug logging for copy/paste operations
              if (displayValue !== calculatedValue) {
                console.log(
                  `Cell ${columnToLetter(colNum)}${rowNum + 1} - Raw:`,
                  calculatedValue,
                  "Display:",
                  displayValue
                );
              }

              // For formula cells, try to inherit formatting from referenced cells
              let inheritedFormat = null;
              if (formula && typeof calculatedValue === "number") {
                // Check if formula references cells with currency formatting
                const cellRefs = formula.match(/[A-Z]+\d+/g);
                if (cellRefs) {
                  for (const cellRef of cellRefs) {
                    const refIndices = cellRefToIndices(cellRef);
                    if (refIndices) {
                      const refCell =
                        existingCellData?.[refIndices.rowIndex]?.[
                          refIndices.colIndex
                        ];
                      if (
                        refCell?.s?.n &&
                        (refCell.s.n.includes("$") ||
                          refCell.s.n.includes("currency"))
                      ) {
                        inheritedFormat = refCell.s.n;
                        break;
                      }
                      // Also check if the referenced cell's display value has currency formatting
                      const refRange = sheet.getRange(
                        refIndices.rowIndex,
                        refIndices.colIndex
                      );
                      const refDisplayValue = refRange.getDisplayValue();
                      if (
                        refDisplayValue &&
                        typeof refDisplayValue === "string" &&
                        refDisplayValue.includes("$")
                      ) {
                        inheritedFormat = '"$"#,##0';
                        break;
                      }
                    }
                  }
                }
              }

              const newCellData = {
                v: calculatedValue, // Raw value
                m: displayValue, // Formatted display value
                t: 1,
                s: {
                  ...(existingCell?.s || {}),
                  // Preserve number formatting if it exists, or use inherited format
                  ...(cellStyle.n && { n: cellStyle.n }),
                  ...(inheritedFormat &&
                    !cellStyle.n && { n: inheritedFormat }),
                  ht: 1, // Ensure left alignment
                },
              };

              if (formula) {
                newCellData.f = formula;

                // If we have an inherited format, apply it to the cell in UniverJS
                if (inheritedFormat && typeof calculatedValue === "number") {
                  try {
                    range.setNumberFormat(inheritedFormat);
                    // Update the display value with the new formatting
                    const newDisplayValue = range.getDisplayValue();
                    if (
                      newDisplayValue &&
                      newDisplayValue !== calculatedValue
                    ) {
                      newCellData.m = newDisplayValue;
                    }
                  } catch (e) {
                    console.log("Could not apply inherited format:", e);
                  }
                }
              }

              if (isMaskedCell) {
                newCellData.s = {
                  ...newCellData.s,
                  bg: { rgb: "#FFFF00" },
                  ht: 1,
                };
                if (highlightAllMaskedCells) {
                  newCellData.v = "";
                }
              }

              updatedCellData[rowNum][colNum] = newCellData;
            }
          }
        }

        // Clear pending changes as they've been processed
        pendingChangesRef.current.clear();

        workbookDataRef.current = {
          ...refWorkbook,
          sheets: {
            ...refWorkbook.sheets,
            ["sheet-01"]: {
              ...refWorkbook.sheets["sheet-01"],
              cellData: updatedCellData,
            },
          },
        };

        SetExcelApiData(transformMatrixToApi(updatedCellData));

        if (highlightAllMaskedCells) {
          SetCellsFilled(false);
        } else {
          let anyFilled = false;
          for (const cellRef of maskedCells) {
            const { rowIndex, colIndex } = cellRefToIndices(cellRef);
            const cell = updatedCellData?.[rowIndex]?.[colIndex];
            if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
              anyFilled = true;
              break;
            }
          }
          SetCellsFilled(anyFilled);
        }
      } catch (error) {
        console.error("Error in handleFormattedDataChange:", error);
      }
    }, [SetExcelApiData, SetCellsFilled, highlightAllMaskedCells, maskedCells]);

    // Debounced version for regular updates
    const handleDataChange = useCallback(() => {
      handleDataChangeImmediate();
    }, [handleDataChangeImmediate]);

    useEffect(() => {
      if (!workbookData || univerCreatedRef.current) return;

      const { univerAPI } = createUniver({
        locale: LocaleType.EN_US,
        locales: { [LocaleType.EN_US]: merge({}, UniverPresetSheetsCoreEnUS) },
        theme: defaultTheme,
        presets: [UniverSheetsCorePreset()],
      });

      univerAPIRef.current = univerAPI;
      univerAPI.createWorkbook(workbookData);

      const initialCellData = workbookData.sheets["sheet-01"].cellData;
      SetExcelApiData(transformMatrixToApi(initialCellData));

      if (highlightAllMaskedCells) {
        SetCellsFilled(false);
      } else {
        let anyMaskedFilled = false;
        for (const cellRef of maskedCells) {
          const { rowIndex, colIndex } = cellRefToIndices(cellRef);
          const cell = initialCellData?.[rowIndex]?.[colIndex];
          if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
            anyMaskedFilled = true;
            break;
          }
        }
        SetCellsFilled(anyMaskedFilled);
      }

      const restrictPermissionOnCells = async () => {
        const book = univerAPI?.getActiveWorkbook();
        const sheet = book.getActiveSheet();
        const bookId = book.getId();
        const sheetId = sheet.getSheetId();
        const permission = book.getPermission();
        const allCells = [];
        for (let row = 1; row <= 100; row++) {
          for (let col = 0; col < 26; col++) {
            allCells.push(`${String.fromCharCode(65 + col)}${row}`);
          }
        }
        const cellsToLock = allCells.filter(
          (cell) => !maskedCells.includes(cell)
        );
        const ranges = cellsToLock.map((cell) => sheet.getRange(cell));
        const { permissionId } = await permission.addRangeBaseProtection(
          bookId,
          sheetId,
          ranges
        );
        const editPoint =
          permission.permissionPointsDefinition
            .RangeProtectionPermissionEditPoint;
        permission.rangeRuleChangedAfterAuth$.subscribe((id) => {
          if (id === permissionId) {
            permission.setRangeProtectionPermissionPoint(
              bookId,
              sheetId,
              permissionId,
              editPoint,
              false
            );
          }
        });
      };

      restrictPermissionOnCells();

      // Create debounced version with longer delay and store reference
      const debouncedHandleDataChange = debounce(handleDataChange, 100);
      debouncedHandleRef.current = debouncedHandleDataChange;

      univerAPI.addEvent(univerAPI.Event.SheetEditChanging, (params) => {
        const newValue = params?.value?._data?.body?.dataStream;
        const row = params?.row;
        const column = params?.column;
        if (newValue === undefined || row === undefined || column === undefined)
          return;

        const trimmedValue = String(newValue).trim();
        const cellKey = `${row}-${column}`;

        // Store pending change
        pendingChangesRef.current.set(cellKey, trimmedValue);

        const refWorkbook = workbookDataRef.current;
        const cellData = refWorkbook?.sheets?.["sheet-01"]?.cellData || {};
        const updatedCellData = { ...cellData };
        if (!updatedCellData[row]) updatedCellData[row] = {};
        const existingCell = updatedCellData[row][column] || {};

        const isMaskedCell = maskedCells.some((cellRef) => {
          const { rowIndex, colIndex } = cellRefToIndices(cellRef);
          return rowIndex === row && colIndex === column;
        });

        if (isMaskedCell) {
          if (highlightAllMaskedCells) {
            updatedCellData[row][column] = {
              ...existingCell,
              v: "",
              t: 1,
              s: {
                bg: { rgb: "#FFFF00" },
                ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
              },
            };
          } else {
            updatedCellData[row][column] = {
              ...existingCell,
              v: trimmedValue,
              t: 1,
              s: {
                bg: { rgb: "#FFFF00" },
                ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
              },
            };
          }
        } else {
          updatedCellData[row][column] = {
            ...existingCell,
            v: trimmedValue,
            t: 1,
            s: {
              ...(existingCell?.s || {}),
              ht: 1, // Left align text (1 = left, 2 = center, 3 = right)
            },
          };
        }

        workbookDataRef.current = {
          ...refWorkbook,
          sheets: {
            ...refWorkbook.sheets,
            ["sheet-01"]: {
              ...refWorkbook.sheets["sheet-01"],
              cellData: updatedCellData,
            },
          },
        };

        debouncedHandleDataChange();
      });

      // Use the enhanced data change handler for better formatting preservation
      const debouncedFormattedDataChange = debounce(
        handleFormattedDataChange,
        100
      );

      // Aggressive formula formatting handler - triggers on ANY value change
      const handleFormulaFormatting = () => {
        const book = univerAPI?.getActiveWorkbook();
        const sheet = book?.getActiveSheet();
        if (!sheet) return;

        // Check all cells for formulas that need formatting
        const sheetData = sheet.getSheet().getSnapshot();
        const maxRow = Math.min(sheetData.rowCount - 1, 100); // Limit to reasonable range
        const maxCol = Math.min(sheetData.columnCount - 1, 26);

        for (let rowNum = 0; rowNum <= maxRow; rowNum++) {
          for (let colNum = 0; colNum <= maxCol; colNum++) {
            const range = sheet.getRange(rowNum, colNum);
            const formula = range.getFormula();
            const calculatedValue = range.getValue();

            if (
              formula &&
              formula.startsWith("=") &&
              typeof calculatedValue === "number"
            ) {
              const cellRefs = formula.match(/[A-Z]+\d+/g);
              if (cellRefs) {
                let shouldApplyCurrencyFormat = false;

                for (const cellRef of cellRefs) {
                  const refIndices = cellRefToIndices(cellRef);
                  if (refIndices) {
                    const refRange = sheet.getRange(
                      refIndices.rowIndex,
                      refIndices.colIndex
                    );
                    const refDisplayValue = refRange.getDisplayValue();
                    if (
                      refDisplayValue &&
                      typeof refDisplayValue === "string" &&
                      refDisplayValue.includes("$")
                    ) {
                      shouldApplyCurrencyFormat = true;
                      break;
                    }
                  }
                }

                if (shouldApplyCurrencyFormat) {
                  const currentDisplayValue = range.getDisplayValue();
                  // Only apply if not already formatted
                  if (
                    !currentDisplayValue ||
                    !currentDisplayValue.includes("$")
                  ) {
                    try {
                      range.setNumberFormat('"$"#,##0');
                      console.log(
                        `Applied currency format to ${columnToLetter(colNum)}${
                          rowNum + 1
                        } = ${calculatedValue} -> $${calculatedValue.toLocaleString()}`
                      );
                    } catch (e) {
                      console.log("Could not apply currency format:", e);
                    }
                  }
                }
              }
            }
          }
        }
      };

      // SIMPLE AND DIRECT: Force save all current cell data after drag
      const captureAllFormulaResults = () => {
        console.log("🚀 DRAG DETECTED - SAVING ALL CELL DATA NOW!");

        // Force immediate data capture - no delays, no complications
        handleDataChangeImmediate();

        // Also trigger the formatted version as backup
        setTimeout(() => {
          handleFormattedDataChange();
          console.log("✅ DRAG DATA SAVED!");
        }, 100);
      };

      // Trigger formula formatting on multiple events
      const debouncedFormulaFormatting = debounce(handleFormulaFormatting, 200);

      // Listen to SheetValueChanged for immediate formatting
      univerAPI.addEvent(univerAPI.Event.SheetValueChanged, () => {
        debouncedFormulaFormatting();
        debouncedFormattedDataChange();
      });

      // AGGRESSIVE DRAG DETECTION - Capture data on ANY mouse activity
      const containerElement = document.getElementById(excelID);
      if (containerElement) {
        let isDragging = false;
        let dragTimer = null;

        const handleMouseDown = (e) => {
          if (e.target.closest(".univer-container")) {
            isDragging = false;
            // Clear any existing timer
            if (dragTimer) {
              clearTimeout(dragTimer);
              dragTimer = null;
            }
          }
        };

        const handleMouseMove = (e) => {
          if (e.buttons === 1) {
            isDragging = true;
          }
        };

        const handleMouseUp = () => {
          if (isDragging) {
            console.log("🎯 DRAG DETECTED - SAVING DATA IN 3 ATTEMPTS!");

            // Multiple capture attempts with different timings
            setTimeout(() => {
              console.log("📊 Attempt 1 - Immediate capture");
              captureAllFormulaResults();
            }, 100);

            setTimeout(() => {
              console.log("📊 Attempt 2 - Medium delay capture");
              captureAllFormulaResults();
            }, 300);

            setTimeout(() => {
              console.log("📊 Attempt 3 - Final capture");
              captureAllFormulaResults();
            }, 600);

            isDragging = false;
          }
        };

        containerElement.addEventListener("mousedown", handleMouseDown);
        containerElement.addEventListener("mousemove", handleMouseMove);
        containerElement.addEventListener("mouseup", handleMouseUp);

        // Store references for cleanup
        containerElement._dragHandlers = {
          mousedown: handleMouseDown,
          mousemove: handleMouseMove,
          mouseup: handleMouseUp,
        };

        // Also add paste event listener to the same container
        const handlePaste = () => {
          console.log("Paste event detected, preserving formatting...");
          // Delay the formatting preservation to allow paste to complete
          setTimeout(() => {
            debouncedFormattedDataChange();
          }, 100);
        };

        containerElement.addEventListener("paste", handlePaste);

        // Store all references for cleanup
        containerElement._pasteHandler = handlePaste;
      }

      // Also try to listen for any available fill/drag events
      try {
        // Try different possible event names for drag operations
        const possibleEvents = [
          "SheetFillSeries",
          "SheetAutofill",
          "SheetDragFill",
          "SheetCellsChanged",
        ];

        possibleEvents.forEach((eventName) => {
          if (univerAPI.Event[eventName]) {
            univerAPI.addEvent(univerAPI.Event[eventName], (params) => {
              console.log(`${eventName} event triggered:`, params);
              setTimeout(() => {
                debouncedFormulaFormatting();
                debouncedFormattedDataChange();
              }, 300);
            });
          }
        });
      } catch (e) {
        console.log("Some drag events not available:", e);
      }

      // Listen for paste events if available
      try {
        if (univerAPI.Event.ClipboardPasted) {
          univerAPI.addEvent(
            univerAPI.Event.ClipboardPasted,
            debouncedFormattedDataChange
          );
        }
      } catch (e) {
        console.log("ClipboardPasted event not available");
      }

      univerCreatedRef.current = true;

      return () => {
        // Cancel any pending debounced calls
        if (debouncedHandleRef.current?.cancel) {
          debouncedHandleRef.current.cancel();
        }

        // Clean up DOM event listeners
        const containerElement = document.getElementById(excelID);
        if (containerElement) {
          // Clean up paste handler
          if (containerElement._pasteHandler) {
            containerElement.removeEventListener(
              "paste",
              containerElement._pasteHandler
            );
            delete containerElement._pasteHandler;
          }

          // Clean up drag handlers
          if (containerElement._dragHandlers) {
            containerElement.removeEventListener(
              "mousedown",
              containerElement._dragHandlers.mousedown
            );
            containerElement.removeEventListener(
              "mousemove",
              containerElement._dragHandlers.mousemove
            );
            containerElement.removeEventListener(
              "mouseup",
              containerElement._dragHandlers.mouseup
            );
            delete containerElement._dragHandlers;
          }
        }

        univerAPIRef.current?.dispose?.();
        univerAPIRef.current = null;
        univerCreatedRef.current = false;
        pendingChangesRef.current.clear();
      };
    }, [
      workbookData,
      excelID,
      SetExcelApiData,
      SetCellsFilled,
      highlightAllMaskedCells,
      maskedCells,
      handleDataChange,
      handleFormattedDataChange,
    ]);

    return (
      <div>
        <div className="univer-container" id={excelID} />
        <ToastContainer transition={Zoom} />
      </div>
    );
  }
);

ExcelSheets.displayName = "ExcelSheets";

export default ExcelSheets;
